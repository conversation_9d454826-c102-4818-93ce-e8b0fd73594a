#!/usr/bin/env python3
"""
Monte Carlo algorithm for value function estimation
"""
import numpy as np


def monte_carlo(env, V, policy, episodes=5000, max_steps=100, alpha=0.1, gamma=0.99):
    """
    Performs the <PERSON> Carlo algorithm for value function estimation

    Args:
        env: environment instance
        V: numpy.ndarray of shape (s,) containing the value estimate
        policy: function that takes in a state and returns the next action to take
        episodes: total number of episodes to train over
        max_steps: maximum number of steps per episode
        alpha: learning rate
        gamma: discount rate

    Returns:
        V: the updated value estimate
    """
    # Make a copy of V to avoid modifying the original
    V = V.copy()

    for _ in range(episodes):
        # Generate an episode
        episode_states = []
        episode_rewards = []

        # Reset environment and get initial state
        state, _ = env.reset()

        # Generate episode trajectory
        for step in range(max_steps):
            episode_states.append(state)

            # Get action from policy
            action = policy(state)

            # Take action in environment
            next_state, reward, terminated, truncated, _ = env.step(action)
            episode_rewards.append(reward)

            # Check if episode is done
            if terminated or truncated:
                break

            state = next_state

        # Calculate returns and update value function (every-visit Monte Carlo)
        G = 0
        # Work backwards through the episode
        for t in range(len(episode_rewards) - 1, -1, -1):
            G = gamma * G + episode_rewards[t]
            state_t = episode_states[t]

            # Update value function using incremental update
            V[state_t] = V[state_t] + alpha * (G - V[state_t])

    return V
