#!/usr/bin/env python3
"""
SARSA(λ) algorithm for Q-learning with eligibility traces
"""
import numpy as np


def sarsa_lambtha(env, Q, lambtha, episodes=5000, max_steps=100, alpha=0.1, gamma=0.99, epsilon=1, min_epsilon=0.1, epsilon_decay=0.05):
    """
    Performs SARSA(λ) algorithm for Q-learning with eligibility traces
    
    Args:
        env: environment instance
        Q: numpy.ndarray of shape (s,a) containing the Q table
        lambtha: eligibility trace factor
        episodes: total number of episodes to train over
        max_steps: maximum number of steps per episode
        alpha: learning rate
        gamma: discount rate
        epsilon: initial threshold for epsilon greedy
        min_epsilon: minimum value that epsilon should decay to
        epsilon_decay: decay rate for updating epsilon between episodes
    
    Returns:
        Q: the updated Q table
    """
    # Make a copy of Q to avoid modifying the original
    Q = Q.copy()

    # Initialize epsilon for epsilon-greedy policy
    current_epsilon = epsilon
    
    for episode in range(episodes):
        # Initialize eligibility traces for this episode
        eligibility_traces = np.zeros_like(Q)
        
        # Reset environment and get initial state
        state, _ = env.reset()
        
        # Choose initial action using epsilon-greedy policy
        if np.random.random() < current_epsilon:
            action = env.action_space.sample()
        else:
            action = np.argmax(Q[state])
        
        # Generate episode trajectory
        for step in range(max_steps):
            # Take action in environment
            next_state, reward, terminated, truncated, _ = env.step(action)
            
            # Choose next action using epsilon-greedy policy
            if np.random.random() < current_epsilon:
                next_action = env.action_space.sample()
            else:
                next_action = np.argmax(Q[next_state])
            
            # Calculate TD error
            td_error = reward + gamma * Q[next_state, next_action] - Q[state, action]
            
            # Update eligibility trace for current state-action pair
            eligibility_traces[state, action] += 1
            
            # Update all Q-values based on their eligibility traces
            Q += alpha * td_error * eligibility_traces
            
            # Decay eligibility traces
            eligibility_traces *= gamma * lambtha
            
            # Check if episode is done
            if terminated or truncated:
                break
            
            # Move to next state and action
            state = next_state
            action = next_action
        
        # Decay epsilon
        current_epsilon = max(min_epsilon, current_epsilon * (1 - epsilon_decay))
    
    return Q
